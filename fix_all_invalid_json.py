#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扫描所有指定字段，将不是JSON格式的字段设置为null
"""

import mysql.connector
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_all_invalid_json.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        logger.info("数据库连接成功")
        return connection, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def is_valid_json(text):
    """检查文本是否为有效的JSON格式"""
    if not text or text.strip() == '':
        return True  # 空值或空字符串认为是有效的
    
    try:
        json.loads(text)
        return True
    except (json.JSONDecodeError, TypeError, ValueError):
        return False

def scan_and_fix_all_invalid_json(dry_run=True):
    """扫描并修复所有无效的JSON字段"""
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        # 需要检查的JSON字段列表
        json_fields = [
            'new_content',
            'transit_chart_content',
            'combination_chart_content',
            'thirdprogressed_chart_content',
            'secondarylimit_chart_content',
            'lunarreturn_chart_content',
            'solarreturn_chart_content',
            'solararc_chart_content',
            'developed_chart_content',
            'smalllimit_chart_content',
            'nataltwelvepointer_chart_content',
            'natalthirteenpointer_chart_content',
            'current_chart_content',
            'chart_content_markdown',
            'comparision_a_chart_content',
            'comparision_b_chart_content',
            'compositeThirprogr_chart_content',
            'compositesecondary_chart_content',
            'marks_a_chart_content',
            'marks_b_chart_content',
            'marksthirprogr_a_chart_content',
            'marksthirprogr_b_chart_content',
            'markssecprogr_a_chart_content',
            'markssecprogr_b_chart_content',
            'timesmidpoint_chart_content',
            'timesmidpointthirprogr_chart_content',
            'timesmidpointsecprogr_chart_content'
        ]
        
        logger.info("开始扫描所有记录的JSON字段...")
        
        if dry_run:
            logger.info("=== 预演模式 (不会实际修改数据) ===")
        else:
            logger.info("=== 实际修复模式 ===")
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) as total FROM corpus_en")
        total_records = cursor.fetchone()['total']
        logger.info(f"总记录数: {total_records}")
        
        # 分批处理记录
        batch_size = 50
        offset = 0
        total_fixed_records = 0
        total_fixed_fields = 0
        invalid_records = []
        
        while offset < total_records:
            # 获取一批记录
            fields_str = ', '.join(['id'] + json_fields)
            query = f"SELECT {fields_str} FROM corpus_en ORDER BY id LIMIT {batch_size} OFFSET {offset}"
            
            cursor.execute(query)
            records = cursor.fetchall()
            
            if not records:
                break
            
            logger.info(f"处理第 {offset + 1} - {offset + len(records)} 条记录")
            
            # 处理这批记录
            for record in records:
                record_id = record['id']
                invalid_fields = []
                
                # 检查每个JSON字段
                for field in json_fields:
                    field_value = record.get(field)
                    if field_value and not is_valid_json(field_value):
                        invalid_fields.append(field)
                
                # 如果有无效字段，记录下来
                if invalid_fields:
                    invalid_record_info = {
                        'id': record_id,
                        'invalid_fields': invalid_fields
                    }
                    invalid_records.append(invalid_record_info)
                    
                    if dry_run:
                        logger.info(f"[预演] 记录 {record_id}: 发现 {len(invalid_fields)} 个无效JSON字段 ({', '.join(invalid_fields)})")
                    else:
                        # 实际修复
                        try:
                            # 构建UPDATE语句
                            update_parts = [f"{field} = NULL" for field in invalid_fields]
                            update_query = f"""
                            UPDATE corpus_en 
                            SET {', '.join(update_parts)}
                            WHERE id = %s
                            """
                            
                            cursor.execute(update_query, (record_id,))
                            
                            logger.info(f"修复记录 {record_id}: 设置 {len(invalid_fields)} 个字段为NULL ({', '.join(invalid_fields)})")
                            total_fixed_records += 1
                            total_fixed_fields += len(invalid_fields)
                            
                        except Exception as e:
                            logger.error(f"修复记录 {record_id} 失败: {e}")
            
            # 如果是实际修复模式，提交这批修改
            if not dry_run:
                connection.commit()
                logger.info(f"已提交第 {offset + 1} - {offset + len(records)} 条记录的修改")
            
            offset += len(records)
        
        # 输出汇总结果
        logger.info("\n" + "=" * 60)
        logger.info("扫描结果汇总:")
        logger.info(f"总记录数: {total_records}")
        logger.info(f"包含无效JSON的记录数: {len(invalid_records)}")
        
        if dry_run:
            # 统计每个字段的无效数量
            field_count = {}
            for record_info in invalid_records:
                for field in record_info['invalid_fields']:
                    field_count[field] = field_count.get(field, 0) + 1
            
            logger.info("\n各字段无效JSON统计:")
            for field, count in sorted(field_count.items()):
                logger.info(f"  {field}: {count} 条记录")
            
            total_invalid_fields = sum(len(r['invalid_fields']) for r in invalid_records)
            logger.info(f"\n[预演] 将修复 {len(invalid_records)} 条记录，共 {total_invalid_fields} 个字段")
        else:
            logger.info(f"实际修复完成！")
            logger.info(f"修复的记录数: {total_fixed_records}")
            logger.info(f"修复的字段数: {total_fixed_fields}")
        
    except Exception as e:
        logger.error(f"扫描修复失败: {e}")
        if connection and not dry_run:
            connection.rollback()
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("扫描并修复所有无效的JSON字段")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    try:
        print("\n请选择操作:")
        print("1. 预演扫描 - 查看哪些字段需要修复")
        print("2. 实际修复 - 将所有无效JSON字段设置为NULL")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-2): ").strip()
        
        if choice == '1':
            scan_and_fix_all_invalid_json(dry_run=True)
            
        elif choice == '2':
            print(f"\n⚠️  警告：这将修改数据库，将所有无效JSON字段设置为NULL")
            print("建议先运行选项1查看要修复的内容")
            confirm = input("\n确认继续？(y/n): ").strip().lower()
            
            if confirm in ['y', 'yes', '是']:
                scan_and_fix_all_invalid_json(dry_run=False)
            else:
                logger.info("操作已取消")
                
        elif choice == '0':
            logger.info("退出程序")
            
        else:
            logger.error("无效的选择")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
